'use client';

import React from 'react';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/Button';
import { Card, CardHeader, CardContent } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';

export default function SignupPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 to-white flex items-center justify-center p-4">
      <div className="w-full max-w-4xl">
        {/* Header */}
        <div className="text-center mb-12">
          <Link href="/" className="inline-flex items-center space-x-2 mb-6">
            <div className="h-10 w-10 rounded-lg bg-primary-600 flex items-center justify-center">
              <span className="text-white font-bold text-xl">E</span>
            </div>
            <span className="text-2xl font-bold text-text-primary">ExamPro</span>
          </Link>
          <h1 className="text-3xl font-bold text-text-primary mb-4">Choose Your Role</h1>
          <p className="text-lg text-text-secondary max-w-2xl mx-auto">
            Select your role to create an account with the appropriate permissions and features.
          </p>
        </div>

        {/* Role Selection Grid */}
        <div className="grid md:grid-cols-2 gap-6 mb-8">
          {/* Student Registration */}
          <Card variant="elevated" className="hover:shadow-strong transition-all duration-200">
            <CardHeader>
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-success-100 rounded-lg flex items-center justify-center">
                  <span className="text-success-600 text-2xl">👨‍🎓</span>
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-text-primary">Student</h3>
                  <Badge variant="success" size="sm">Open Registration</Badge>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-text-secondary mb-6">
                Take exams, view results, and manage your academic progress with secure monitoring.
              </p>
              <ul className="space-y-2 text-sm text-text-secondary mb-6">
                <li className="flex items-center">
                  <span className="text-success-500 mr-2">✓</span>
                  Access to assigned exams
                </li>
                <li className="flex items-center">
                  <span className="text-success-500 mr-2">✓</span>
                  Real-time results and feedback
                </li>
                <li className="flex items-center">
                  <span className="text-success-500 mr-2">✓</span>
                  Progress tracking
                </li>
                <li className="flex items-center">
                  <span className="text-success-500 mr-2">✓</span>
                  Secure exam environment
                </li>
              </ul>
              <Button asChild className="w-full" size="lg">
                <Link href="/signup/student">Register as Student</Link>
              </Button>
            </CardContent>
          </Card>

          {/* Instructor Registration */}
          <Card variant="elevated" className="hover:shadow-strong transition-all duration-200">
            <CardHeader>
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                  <span className="text-primary-600 text-2xl">👨‍🏫</span>
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-text-primary">Instructor</h3>
                  <Badge variant="primary" size="sm">Invitation Required</Badge>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-text-secondary mb-6">
                Create and manage exams, build question banks, and analyze student performance.
              </p>
              <ul className="space-y-2 text-sm text-text-secondary mb-6">
                <li className="flex items-center">
                  <span className="text-primary-500 mr-2">✓</span>
                  Create and edit exams
                </li>
                <li className="flex items-center">
                  <span className="text-primary-500 mr-2">✓</span>
                  Question bank management
                </li>
                <li className="flex items-center">
                  <span className="text-primary-500 mr-2">✓</span>
                  Student analytics
                </li>
                <li className="flex items-center">
                  <span className="text-primary-500 mr-2">✓</span>
                  Grade management
                </li>
              </ul>
              <Button asChild variant="outline" className="w-full" size="lg">
                <Link href="/signup/instructor">Register as Instructor</Link>
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Staff Roles - Hidden by default */}
        <div className="text-center mb-8">
          <details className="group">
            <summary className="cursor-pointer text-text-secondary hover:text-text-primary transition-colors">
              <span className="text-sm font-medium">Staff & Administrative Roles</span>
              <span className="ml-2 group-open:rotate-180 transition-transform inline-block">▼</span>
            </summary>
            
            <div className="mt-6 grid md:grid-cols-2 gap-6">
              {/* Proctor Registration */}
              <Card variant="outlined">
                <CardHeader>
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-warning-100 rounded-lg flex items-center justify-center">
                      <span className="text-warning-600 text-2xl">🛡️</span>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-text-primary">Proctor</h3>
                      <Badge variant="warning" size="sm">Staff Only</Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-text-secondary mb-4">
                    Monitor exam sessions and ensure academic integrity with AI-powered tools.
                  </p>
                  <Button asChild variant="outline" className="w-full">
                    <Link href="/signup/proctor">Register as Proctor</Link>
                  </Button>
                </CardContent>
              </Card>

              {/* Admin Registration */}
              <Card variant="outlined">
                <CardHeader>
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-error-100 rounded-lg flex items-center justify-center">
                      <span className="text-error-600 text-2xl">⚙️</span>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-text-primary">Administrator</h3>
                      <Badge variant="error" size="sm">Admin Only</Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-text-secondary mb-4">
                    Manage users, system settings, and oversee the entire platform.
                  </p>
                  <Button asChild variant="outline" className="w-full">
                    <Link href="/signup/admin">Register as Admin</Link>
                  </Button>
                </CardContent>
              </Card>
            </div>
          </details>
        </div>

        {/* Login Link */}
        <div className="text-center">
          <span className="text-text-secondary">
            Already have an account?{' '}
            <Link
              href="/login"
              className="text-primary-600 hover:text-primary-700 font-medium"
            >
              Sign in here
            </Link>
          </span>
        </div>
      </div>
    </div>
  );
}
