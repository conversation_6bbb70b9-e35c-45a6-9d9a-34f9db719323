import Link from 'next/link';
import { Button } from '@/components/ui/Button';
import { <PERSON>, CardHeader, CardContent } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 to-white">
      {/* Header */}
      <header className="border-b border-border-light bg-white/80 backdrop-blur-sm">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex h-16 items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="h-8 w-8 rounded-lg bg-primary-600 flex items-center justify-center">
                <span className="text-white font-bold text-lg">E</span>
              </div>
              <span className="text-xl font-bold text-text-primary">ExamPro</span>
            </div>
            <div className="flex items-center space-x-4">
              <Button variant="ghost" size="sm" asChild>
                <Link href="/login">Login</Link>
              </Button>
              <Button size="sm" asChild>
                <Link href="/signup">Sign Up</Link>
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <main className="container mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="text-center max-w-4xl mx-auto">
          <Badge variant="primary" size="lg" className="mb-6">
            🚀 Now with AI-Powered Proctoring
          </Badge>

          <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-text-primary mb-6">
            Secure Online Exams
            <span className="text-gradient block">Made Simple</span>
          </h1>

          <p className="text-lg sm:text-xl text-text-secondary mb-8 max-w-2xl mx-auto">
            Create, manage, and monitor online exams with advanced proctoring features.
            Built for educational institutions and organizations.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-16">
            <Button size="lg" asChild>
              <Link href="/signup">Get Started Free</Link>
            </Button>
            <Button variant="outline" size="lg" asChild>
              <Link href="/demo">Watch Demo</Link>
            </Button>
          </div>
        </div>

        {/* Features Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
          <Card variant="elevated" className="text-center">
            <CardHeader>
              <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <span className="text-primary-600 text-2xl">👨‍🎓</span>
              </div>
            </CardHeader>
            <CardContent>
              <h3 className="text-lg font-semibold text-text-primary mb-2">For Students</h3>
              <p className="text-text-secondary">
                Take exams securely with real-time monitoring and instant results.
              </p>
            </CardContent>
          </Card>

          <Card variant="elevated" className="text-center">
            <CardHeader>
              <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <span className="text-primary-600 text-2xl">👨‍🏫</span>
              </div>
            </CardHeader>
            <CardContent>
              <h3 className="text-lg font-semibold text-text-primary mb-2">For Instructors</h3>
              <p className="text-text-secondary">
                Create comprehensive exams with our intuitive question bank system.
              </p>
            </CardContent>
          </Card>

          <Card variant="elevated" className="text-center">
            <CardHeader>
              <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <span className="text-primary-600 text-2xl">🛡️</span>
              </div>
            </CardHeader>
            <CardContent>
              <h3 className="text-lg font-semibold text-text-primary mb-2">For Proctors</h3>
              <p className="text-text-secondary">
                Monitor multiple exam sessions with AI-powered flagging system.
              </p>
            </CardContent>
          </Card>
        </div>

        {/* CTA Section */}
        <div className="text-center mt-16">
          <Card variant="elevated" className="max-w-2xl mx-auto bg-gradient-to-r from-primary-600 to-primary-700 text-white border-0">
            <CardContent className="p-8">
              <h2 className="text-2xl font-bold mb-4">Ready to get started?</h2>
              <p className="text-primary-100 mb-6">
                Join thousands of institutions already using ExamPro for secure online assessments.
              </p>
              <Button variant="secondary" size="lg" asChild>
                <Link href="/signup">Start Your Free Trial</Link>
              </Button>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
}
