'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { <PERSON>, CardHeader, CardContent, CardFooter } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';

interface LoginForm {
  email: string;
  password: string;
}

export default function LoginPage() {
  const router = useRouter();
  const [form, setForm] = useState<LoginForm>({ email: '', password: '' });
  const [errors, setErrors] = useState<Partial<LoginForm>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [generalError, setGeneralError] = useState('');

  const handleInputChange = (field: keyof LoginForm) => (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    setForm(prev => ({ ...prev, [field]: e.target.value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
    if (generalError) {
      setGeneralError('');
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<LoginForm> = {};

    if (!form.email) {
      newErrors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    if (!form.password) {
      newErrors.password = 'Password is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    setGeneralError('');

    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(form),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Login failed');
      }

      // Store token in localStorage (in a real app, consider using httpOnly cookies)
      localStorage.setItem('token', data.token);
      
      // Redirect based on user role
      router.push(data.redirectPath);
    } catch (error) {
      setGeneralError(error instanceof Error ? error.message : 'An error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 to-white flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Header */}
        <div className="text-center mb-8">
          <Link href="/" className="inline-flex items-center space-x-2 mb-6">
            <div className="h-10 w-10 rounded-lg bg-primary-600 flex items-center justify-center">
              <span className="text-white font-bold text-xl">E</span>
            </div>
            <span className="text-2xl font-bold text-text-primary">ExamPro</span>
          </Link>
          <h1 className="text-2xl font-bold text-text-primary mb-2">Welcome back</h1>
          <p className="text-text-secondary">Sign in to your account to continue</p>
        </div>

        {/* Login Form */}
        <Card variant="elevated">
          <form onSubmit={handleSubmit}>
            <CardHeader>
              <div className="flex items-center justify-center space-x-2 mb-4">
                <Badge variant="primary" size="sm">All Roles</Badge>
                <span className="text-text-tertiary">•</span>
                <span className="text-sm text-text-secondary">Unified Login</span>
              </div>
            </CardHeader>
            
            <CardContent className="space-y-4">
              {generalError && (
                <div className="p-3 rounded-lg bg-error-50 border border-error-200">
                  <p className="text-sm text-error-700">{generalError}</p>
                </div>
              )}

              <Input
                label="Email Address"
                type="email"
                value={form.email}
                onChange={handleInputChange('email')}
                error={errors.email}
                placeholder="Enter your email"
                disabled={isLoading}
              />

              <Input
                label="Password"
                type="password"
                value={form.password}
                onChange={handleInputChange('password')}
                error={errors.password}
                placeholder="Enter your password"
                disabled={isLoading}
              />

              <div className="flex items-center justify-between">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    className="rounded border-border-medium text-primary-600 focus:ring-primary-500"
                  />
                  <span className="ml-2 text-sm text-text-secondary">Remember me</span>
                </label>
                <Link
                  href="/forgot-password"
                  className="text-sm text-primary-600 hover:text-primary-700 font-medium"
                >
                  Forgot password?
                </Link>
              </div>
            </CardContent>

            <CardFooter className="flex flex-col space-y-4">
              <Button
                type="submit"
                size="lg"
                className="w-full"
                loading={isLoading}
                disabled={isLoading}
              >
                Sign In
              </Button>

              <div className="text-center">
                <span className="text-sm text-text-secondary">
                  Don't have an account?{' '}
                  <Link
                    href="/signup"
                    className="text-primary-600 hover:text-primary-700 font-medium"
                  >
                    Sign up
                  </Link>
                </span>
              </div>
            </CardFooter>
          </form>
        </Card>

        {/* Role Information */}
        <div className="mt-6 text-center">
          <p className="text-xs text-text-tertiary mb-2">Access levels:</p>
          <div className="flex justify-center space-x-2 flex-wrap">
            <Badge variant="success" size="sm">Student</Badge>
            <Badge variant="primary" size="sm">Instructor</Badge>
            <Badge variant="warning" size="sm">Proctor</Badge>
            <Badge variant="error" size="sm">Admin</Badge>
          </div>
        </div>
      </div>
    </div>
  );
}
