'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { cn } from '@/lib/utils';

interface HeaderProps {
  user?: {
    name: string;
    email: string;
    role: 'STUDENT' | 'INSTRUCTOR' | 'PROCTOR' | 'ADMIN';
    avatar?: string;
  };
}

const Header: React.FC<HeaderProps> = ({ user }) => {
  const pathname = usePathname();
  
  const isActive = (path: string) => pathname === path;
  
  const getRoleColor = (role: string) => {
    switch (role) {
      case 'ADMIN': return 'error';
      case 'PROCTOR': return 'warning';
      case 'INSTRUCTOR': return 'primary';
      case 'STUDENT': return 'success';
      default: return 'default';
    }
  };

  return (
    <header className="sticky top-0 z-50 w-full border-b border-border-light bg-white/95 backdrop-blur supports-[backdrop-filter]:bg-white/60">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex h-16 items-center justify-between">
          {/* Logo */}
          <div className="flex items-center">
            <Link href="/" className="flex items-center space-x-2">
              <div className="h-8 w-8 rounded-lg bg-primary-600 flex items-center justify-center">
                <span className="text-white font-bold text-lg">E</span>
              </div>
              <span className="text-xl font-bold text-text-primary">ExamPro</span>
            </Link>
          </div>

          {/* Navigation */}
          {user && (
            <nav className="hidden md:flex items-center space-x-6">
              {user.role === 'STUDENT' && (
                <>
                  <Link
                    href="/student/dashboard"
                    className={cn(
                      "text-sm font-medium transition-colors hover:text-primary-600",
                      isActive('/student/dashboard') ? "text-primary-600" : "text-text-secondary"
                    )}
                  >
                    Dashboard
                  </Link>
                  <Link
                    href="/student/results"
                    className={cn(
                      "text-sm font-medium transition-colors hover:text-primary-600",
                      isActive('/student/results') ? "text-primary-600" : "text-text-secondary"
                    )}
                  >
                    Results
                  </Link>
                </>
              )}
              
              {user.role === 'INSTRUCTOR' && (
                <>
                  <Link
                    href="/instructor/dashboard"
                    className={cn(
                      "text-sm font-medium transition-colors hover:text-primary-600",
                      isActive('/instructor/dashboard') ? "text-primary-600" : "text-text-secondary"
                    )}
                  >
                    Dashboard
                  </Link>
                  <Link
                    href="/instructor/questions"
                    className={cn(
                      "text-sm font-medium transition-colors hover:text-primary-600",
                      isActive('/instructor/questions') ? "text-primary-600" : "text-text-secondary"
                    )}
                  >
                    Question Bank
                  </Link>
                </>
              )}
              
              {user.role === 'PROCTOR' && (
                <>
                  <Link
                    href="/proctor/dashboard"
                    className={cn(
                      "text-sm font-medium transition-colors hover:text-primary-600",
                      isActive('/proctor/dashboard') ? "text-primary-600" : "text-text-secondary"
                    )}
                  >
                    Dashboard
                  </Link>
                  <Link
                    href="/proctor/flags"
                    className={cn(
                      "text-sm font-medium transition-colors hover:text-primary-600",
                      isActive('/proctor/flags') ? "text-primary-600" : "text-text-secondary"
                    )}
                  >
                    Flags
                  </Link>
                </>
              )}
              
              {user.role === 'ADMIN' && (
                <>
                  <Link
                    href="/admin/dashboard"
                    className={cn(
                      "text-sm font-medium transition-colors hover:text-primary-600",
                      isActive('/admin/dashboard') ? "text-primary-600" : "text-text-secondary"
                    )}
                  >
                    Dashboard
                  </Link>
                  <Link
                    href="/admin/users"
                    className={cn(
                      "text-sm font-medium transition-colors hover:text-primary-600",
                      isActive('/admin/users') ? "text-primary-600" : "text-text-secondary"
                    )}
                  >
                    Users
                  </Link>
                </>
              )}
            </nav>
          )}

          {/* User Menu */}
          <div className="flex items-center space-x-4">
            {user ? (
              <div className="flex items-center space-x-3">
                <Badge variant={getRoleColor(user.role) as any} size="sm">
                  {user.role}
                </Badge>
                <div className="flex items-center space-x-2">
                  <div className="h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center">
                    <span className="text-primary-600 font-medium text-sm">
                      {user.name.charAt(0).toUpperCase()}
                    </span>
                  </div>
                  <span className="text-sm font-medium text-text-primary hidden sm:block">
                    {user.name}
                  </span>
                </div>
              </div>
            ) : (
              <div className="flex items-center space-x-2">
                <Button variant="ghost" size="sm" asChild>
                  <Link href="/login">Login</Link>
                </Button>
                <Button size="sm" asChild>
                  <Link href="/signup">Sign Up</Link>
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  );
};

export { Header };
