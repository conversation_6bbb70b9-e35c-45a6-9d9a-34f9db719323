'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardHeader, CardContent } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/Button';

interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: string;
  profileCompleted: boolean;
}

export default function StudentDashboard() {
  const router = useRouter();
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Check if user is authenticated
    const token = localStorage.getItem('token');
    if (!token) {
      router.push('/login');
      return;
    }

    // In a real app, you would verify the token with the server
    // For now, we'll decode it client-side (not secure for production)
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      setUser({
        id: payload.id,
        email: payload.email,
        firstName: 'Student', // Would come from token or API call
        lastName: 'User',
        role: payload.role,
        profileCompleted: true,
      });
    } catch (error) {
      console.error('Invalid token:', error);
      localStorage.removeItem('token');
      router.push('/login');
      return;
    }

    setLoading(false);
  }, [router]);

  const handleLogout = () => {
    localStorage.removeItem('token');
    router.push('/');
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background-secondary flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
          <p className="text-text-secondary">Loading...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-background-secondary">
      {/* Header */}
      <header className="bg-white border-b border-border-light">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex h-16 items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="h-8 w-8 rounded-lg bg-primary-600 flex items-center justify-center">
                <span className="text-white font-bold text-lg">E</span>
              </div>
              <span className="text-xl font-bold text-text-primary">ExamPro</span>
            </div>
            <div className="flex items-center space-x-4">
              <Badge variant="success" size="sm">{user.role}</Badge>
              <span className="text-sm font-medium text-text-primary">
                {user.firstName} {user.lastName}
              </span>
              <Button variant="ghost" size="sm" onClick={handleLogout}>
                Logout
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-text-primary mb-2">
            Welcome back, {user.firstName}!
          </h1>
          <p className="text-text-secondary">
            Here's your student dashboard with upcoming exams and recent activity.
          </p>
        </div>

        {/* Dashboard Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Upcoming Exams */}
          <Card variant="elevated">
            <CardHeader title="Upcoming Exams" />
            <CardContent>
              <div className="text-center py-8">
                <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-primary-600 text-2xl">📝</span>
                </div>
                <p className="text-text-secondary">No upcoming exams</p>
                <p className="text-sm text-text-tertiary mt-2">
                  Check back later for new assignments
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Recent Results */}
          <Card variant="elevated">
            <CardHeader title="Recent Results" />
            <CardContent>
              <div className="text-center py-8">
                <div className="w-16 h-16 bg-success-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-success-600 text-2xl">📊</span>
                </div>
                <p className="text-text-secondary">No recent results</p>
                <p className="text-sm text-text-tertiary mt-2">
                  Complete exams to see your scores
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Profile Status */}
          <Card variant="elevated">
            <CardHeader title="Profile Status" />
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-text-secondary">Email Verified</span>
                  <Badge variant="success" size="sm">✓</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-text-secondary">Profile Complete</span>
                  <Badge variant={user.profileCompleted ? "success" : "warning"} size="sm">
                    {user.profileCompleted ? "✓" : "!"}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-text-secondary">Device Check</span>
                  <Badge variant="warning" size="sm">Pending</Badge>
                </div>
                <Button variant="outline" size="sm" className="w-full mt-4">
                  Complete Setup
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <div className="mt-8">
          <h2 className="text-xl font-semibold text-text-primary mb-4">Quick Actions</h2>
          <div className="grid sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <Button variant="outline" className="h-20 flex-col">
              <span className="text-2xl mb-2">🔍</span>
              <span>Browse Exams</span>
            </Button>
            <Button variant="outline" className="h-20 flex-col">
              <span className="text-2xl mb-2">📋</span>
              <span>View Results</span>
            </Button>
            <Button variant="outline" className="h-20 flex-col">
              <span className="text-2xl mb-2">⚙️</span>
              <span>Settings</span>
            </Button>
            <Button variant="outline" className="h-20 flex-col">
              <span className="text-2xl mb-2">❓</span>
              <span>Help</span>
            </Button>
          </div>
        </div>
      </main>
    </div>
  );
}
