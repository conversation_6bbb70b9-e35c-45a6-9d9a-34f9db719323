// Online Exam Application Database Schema
// Comprehensive schema for multi-role exam management system

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Enums for various status and role types
enum UserRole {
  STUDENT
  INSTRUCTOR
  PROCTOR
  ADMIN
}

enum ExamStatus {
  DRAFT
  PUBLISHED
  ACTIVE
  COMPLETED
  CANCELLED
}

enum QuestionType {
  MULTIPLE_CHOICE
  TRUE_FALSE
  SHORT_ANSWER
  ESSAY
  FILL_IN_BLANK
  MATCHING
}

enum SubmissionStatus {
  NOT_STARTED
  IN_PROGRESS
  SUBMITTED
  GRADED
  FLAGGED
}

enum FlagType {
  SUSPICIOUS_BEHAVIOR
  TECHNICAL_ISSUE
  CHEATING_ATTEMPT
  UNAUTHORIZED_ACCESS
  NETWORK_ISSUE
  CAMERA_ISSUE
  AUDIO_ISSUE
}

enum FlagSeverity {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

// Core User Management
model User {
  id                String   @id @default(cuid())
  email             String   @unique
  password          String
  firstName         String
  lastName          String
  role              UserRole
  isActive          Boolean  @default(true)
  emailVerified     Boolean  @default(false)
  profileImageUrl   String?
  phoneNumber       String?
  dateOfBirth       DateTime?
  address           String?
  institutionId     String?
  studentId         String?  // For students
  employeeId        String?  // For staff

  // Timestamps
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  lastLoginAt       DateTime?

  // Two-factor authentication
  twoFactorEnabled  Boolean  @default(false)
  twoFactorSecret   String?

  // Profile setup completion
  profileCompleted  Boolean  @default(false)

  // Relationships
  createdExams      Exam[]   @relation("ExamCreator")
  examSubmissions   ExamSubmission[]
  proctoringSessions ProctorSession[]
  flagsCreated      Flag[]   @relation("FlagCreator")
  flagsAssigned     Flag[]   @relation("FlagAssignee")
  auditLogs         AuditLog[]
  notifications     Notification[]
  deviceChecks      DeviceCheck[]
  createdQuestions  Question[]

  @@map("users")
}

// Institution/Organization Management
model Institution {
  id          String   @id @default(cuid())
  name        String
  domain      String   @unique
  address     String?
  contactEmail String?
  contactPhone String?
  isActive    Boolean  @default(true)
  settings    Json?    // Institution-specific settings

  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relationships
  exams       Exam[]

  @@map("institutions")
}

// Exam Management
model Exam {
  id                String     @id @default(cuid())
  title             String
  description       String?
  instructions      String?

  // Exam Configuration
  duration          Int        // Duration in minutes
  totalMarks        Int        @default(0)
  passingMarks      Int        @default(0)
  maxAttempts       Int        @default(1)

  // Scheduling
  startTime         DateTime?
  endTime           DateTime?

  // Access Control
  accessCode        String?    // Optional access code
  allowLateSubmission Boolean  @default(false)
  lateSubmissionPenalty Int    @default(0) // Percentage penalty

  // Proctoring Settings
  requireProctoring Boolean    @default(false)
  allowCameraAccess Boolean    @default(true)
  allowMicAccess    Boolean    @default(true)
  allowScreenShare  Boolean    @default(false)
  preventTabSwitch  Boolean    @default(true)
  preventCopyPaste  Boolean    @default(true)

  // Status and Metadata
  status            ExamStatus @default(DRAFT)
  isPublished       Boolean    @default(false)

  // Relationships
  creatorId         String
  creator           User       @relation("ExamCreator", fields: [creatorId], references: [id])
  institutionId     String?
  institution       Institution? @relation(fields: [institutionId], references: [id])

  questions         ExamQuestion[]
  submissions       ExamSubmission[]
  proctoringSessions ProctorSession[]

  // Timestamps
  createdAt         DateTime   @default(now())
  updatedAt         DateTime   @updatedAt

  @@map("exams")
}

// Question Bank Management
model Question {
  id              String       @id @default(cuid())
  title           String
  content         String       // Question text/content
  type            QuestionType
  difficulty      String?      // Easy, Medium, Hard
  subject         String?
  topic           String?
  tags            String[]     // Array of tags for categorization

  // Question Configuration
  options         Json?        // For multiple choice, matching, etc.
  correctAnswer   Json         // Correct answer(s)
  explanation     String?      // Explanation for the answer
  marks           Int          @default(1)
  negativeMarks   Int          @default(0)

  // Media attachments
  imageUrl        String?
  audioUrl        String?
  videoUrl        String?
  attachmentUrls  String[]

  // Metadata
  isActive        Boolean      @default(true)
  usageCount      Int          @default(0)

  // Relationships
  creatorId       String
  creator         User         @relation(fields: [creatorId], references: [id])
  examQuestions   ExamQuestion[]
  answers         Answer[]

  // Timestamps
  createdAt       DateTime     @default(now())
  updatedAt       DateTime     @updatedAt

  @@map("questions")
}

// Junction table for Exam-Question relationship
model ExamQuestion {
  id          String   @id @default(cuid())
  examId      String
  questionId  String
  order       Int      // Order of question in exam
  marks       Int      // Marks for this question in this exam (can override default)
  isOptional  Boolean  @default(false)

  // Relationships
  exam        Exam     @relation(fields: [examId], references: [id], onDelete: Cascade)
  question    Question @relation(fields: [questionId], references: [id])

  @@unique([examId, questionId])
  @@unique([examId, order])
  @@map("exam_questions")
}

// Exam Submissions and Answers
model ExamSubmission {
  id                String           @id @default(cuid())
  examId            String
  studentId         String

  // Submission Status
  status            SubmissionStatus @default(NOT_STARTED)
  attemptNumber     Int              @default(1)

  // Timing
  startedAt         DateTime?
  submittedAt       DateTime?
  timeSpent         Int              @default(0) // Time spent in minutes

  // Scoring
  totalMarks        Int              @default(0)
  obtainedMarks     Int              @default(0)
  percentage        Float            @default(0)
  grade             String?

  // Flags and Issues
  isFlagged         Boolean          @default(false)
  flagReason        String?

  // Proctoring Data
  proctorSessionId  String?

  // Relationships
  exam              Exam             @relation(fields: [examId], references: [id])
  student           User             @relation(fields: [studentId], references: [id])
  proctorSession    ProctorSession?  @relation(fields: [proctorSessionId], references: [id])
  answers           Answer[]
  flags             Flag[]

  // Timestamps
  createdAt         DateTime         @default(now())
  updatedAt         DateTime         @updatedAt

  @@unique([examId, studentId, attemptNumber])
  @@map("exam_submissions")
}

// Individual Question Answers
model Answer {
  id               String         @id @default(cuid())
  submissionId     String
  questionId       String

  // Answer Data
  answer           Json           // Student's answer
  isCorrect        Boolean?       // Null if not graded yet
  marksObtained    Int            @default(0)

  // Timing
  timeSpent        Int            @default(0) // Time spent on this question in seconds
  answeredAt       DateTime?

  // Relationships
  submission       ExamSubmission @relation(fields: [submissionId], references: [id], onDelete: Cascade)
  question         Question       @relation(fields: [questionId], references: [id])

  // Timestamps
  createdAt        DateTime       @default(now())
  updatedAt        DateTime       @updatedAt

  @@unique([submissionId, questionId])
  @@map("answers")
}

// Proctoring and Monitoring System
model ProctorSession {
  id                String           @id @default(cuid())
  examId            String
  proctorId         String

  // Session Details
  sessionName       String
  maxStudents       Int              @default(50)
  currentStudents   Int              @default(0)

  // Session Status
  isActive          Boolean          @default(false)
  startedAt         DateTime?
  endedAt           DateTime?

  // Monitoring Settings
  recordVideo       Boolean          @default(true)
  recordAudio       Boolean          @default(true)
  recordScreen      Boolean          @default(false)
  detectFaces       Boolean          @default(true)
  detectObjects     Boolean          @default(true)

  // Relationships
  exam              Exam             @relation(fields: [examId], references: [id])
  proctor           User             @relation(fields: [proctorId], references: [id])
  submissions       ExamSubmission[]
  flags             Flag[]

  // Timestamps
  createdAt         DateTime         @default(now())
  updatedAt         DateTime         @updatedAt

  @@map("proctor_sessions")
}

// Flagging System for Suspicious Activities
model Flag {
  id                String         @id @default(cuid())
  type              FlagType
  severity          FlagSeverity   @default(MEDIUM)
  title             String
  description       String

  // Context
  submissionId      String?
  proctorSessionId  String?
  timestamp         DateTime       @default(now())

  // Evidence
  screenshotUrl     String?
  videoUrl          String?
  audioUrl          String?
  metadata          Json?          // Additional context data

  // Resolution
  isResolved        Boolean        @default(false)
  resolution        String?
  resolvedAt        DateTime?

  // Relationships
  createdById       String
  assignedToId      String?
  createdBy         User           @relation("FlagCreator", fields: [createdById], references: [id])
  assignedTo        User?          @relation("FlagAssignee", fields: [assignedToId], references: [id])
  submission        ExamSubmission? @relation(fields: [submissionId], references: [id])
  proctorSession    ProctorSession? @relation(fields: [proctorSessionId], references: [id])

  // Timestamps
  createdAt         DateTime       @default(now())
  updatedAt         DateTime       @updatedAt

  @@map("flags")
}

// Device and Environment Checking
model DeviceCheck {
  id                String   @id @default(cuid())
  userId            String

  // Device Information
  deviceType        String   // Desktop, Mobile, Tablet
  operatingSystem   String
  browser           String
  browserVersion    String
  screenResolution  String

  // Capabilities Check
  cameraAvailable   Boolean  @default(false)
  microphoneAvailable Boolean @default(false)
  networkSpeed      String?

  // Environment Check
  lightingCondition String?  // Good, Poor, Adequate
  noiseLevel        String?  // Quiet, Moderate, Noisy

  // Check Results
  isApproved        Boolean  @default(false)
  issues            String[] // Array of identified issues
  recommendations   String[] // Array of recommendations

  // Relationships
  user              User     @relation(fields: [userId], references: [id])

  // Timestamps
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  @@map("device_checks")
}

// Notification System
model Notification {
  id          String   @id @default(cuid())
  userId      String
  title       String
  message     String
  type        String   // INFO, WARNING, ERROR, SUCCESS
  isRead      Boolean  @default(false)
  actionUrl   String?  // Optional URL for action

  // Relationships
  user        User     @relation(fields: [userId], references: [id])

  // Timestamps
  createdAt   DateTime @default(now())
  readAt      DateTime?

  @@map("notifications")
}

// Audit Logging System
model AuditLog {
  id          String   @id @default(cuid())
  userId      String?
  action      String   // CREATE, UPDATE, DELETE, LOGIN, LOGOUT, etc.
  resource    String   // User, Exam, Question, etc.
  resourceId  String?
  details     Json?    // Additional details about the action
  ipAddress   String?
  userAgent   String?

  // Relationships
  user        User?    @relation(fields: [userId], references: [id])

  // Timestamps
  createdAt   DateTime @default(now())

  @@map("audit_logs")
}
