import { NextRequest, NextResponse } from 'next/server';
import { createUser, generateToken, getRedirectPath, validateEmail, validatePassword, AuthError } from '@/lib/auth';
import { UserRole } from '@prisma/client';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      email, 
      password, 
      firstName, 
      lastName, 
      role, 
      studentId, 
      employeeId, 
      institutionId 
    } = body;

    // Validate required fields
    if (!email || !password || !firstName || !lastName || !role) {
      return NextResponse.json(
        { message: 'All required fields must be provided' },
        { status: 400 }
      );
    }

    // Validate email format
    if (!validateEmail(email)) {
      return NextResponse.json(
        { message: 'Please provide a valid email address' },
        { status: 400 }
      );
    }

    // Validate password strength
    const passwordValidation = validatePassword(password);
    if (!passwordValidation.isValid) {
      return NextResponse.json(
        { 
          message: 'Password does not meet requirements',
          errors: passwordValidation.errors 
        },
        { status: 400 }
      );
    }

    // Validate role
    if (!Object.values(UserRole).includes(role)) {
      return NextResponse.json(
        { message: 'Invalid role specified' },
        { status: 400 }
      );
    }

    // Role-specific validations
    if (role === 'STUDENT' && !studentId) {
      return NextResponse.json(
        { message: 'Student ID is required for student registration' },
        { status: 400 }
      );
    }

    if ((role === 'INSTRUCTOR' || role === 'PROCTOR' || role === 'ADMIN') && !employeeId) {
      // For now, we'll allow registration without employee ID
      // In production, you might want to require invitation codes
    }

    // Create user
    const userData = {
      email: email.toLowerCase().trim(),
      password,
      firstName: firstName.trim(),
      lastName: lastName.trim(),
      role,
      studentId: studentId?.trim(),
      employeeId: employeeId?.trim(),
      institutionId: institutionId?.trim(),
    };

    const user = await createUser(userData);
    
    // Generate JWT token
    const token = generateToken(user);
    
    // Get redirect path based on role
    const redirectPath = getRedirectPath(user.role);

    return NextResponse.json({
      message: 'Account created successfully',
      token,
      user: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
        profileCompleted: user.profileCompleted,
      },
      redirectPath,
    }, { status: 201 });

  } catch (error) {
    console.error('Signup error:', error);
    
    if (error instanceof AuthError) {
      return NextResponse.json(
        { message: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
