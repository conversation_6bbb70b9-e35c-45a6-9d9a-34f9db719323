import { NextRequest, NextResponse } from 'next/server';
import { authenticateUser, generateToken, getRedirectPath, AuthError } from '@/lib/auth';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email, password } = body;

    // Validate input
    if (!email || !password) {
      return NextResponse.json(
        { message: 'Email and password are required' },
        { status: 400 }
      );
    }

    // Authenticate user
    const user = await authenticateUser({ email, password });
    
    // Generate JWT token
    const token = generateToken(user);
    
    // Get redirect path based on role
    const redirectPath = getRedirectPath(user.role);

    return NextResponse.json({
      message: 'Login successful',
      token,
      user: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
        profileCompleted: user.profileCompleted,
      },
      redirectPath,
    });

  } catch (error) {
    console.error('Login error:', error);
    
    if (error instanceof AuthError) {
      return NextResponse.json(
        { message: error.message },
        { status: 401 }
      );
    }

    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
