import React from 'react';
import { cn } from '@/lib/utils';

interface BadgeProps extends React.HTMLAttributes<HTMLSpanElement> {
  variant?: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  dot?: boolean;
}

const Badge = React.forwardRef<HTMLSpanElement, BadgeProps>(
  ({ className, variant = 'default', size = 'md', dot = false, children, ...props }, ref) => {
    const baseStyles = "inline-flex items-center font-medium rounded-full transition-colors duration-200";
    
    const variants = {
      default: "bg-secondary-100 text-secondary-800",
      primary: "bg-primary-100 text-primary-800",
      secondary: "bg-secondary-100 text-secondary-800",
      success: "bg-success-50 text-success-700 border border-success-200",
      warning: "bg-warning-50 text-warning-700 border border-warning-200",
      error: "bg-error-50 text-error-700 border border-error-200",
      outline: "border border-border-medium text-text-secondary bg-white"
    };
    
    const sizes = {
      sm: dot ? "h-2 w-2" : "px-2 py-0.5 text-xs",
      md: dot ? "h-2.5 w-2.5" : "px-2.5 py-0.5 text-xs",
      lg: dot ? "h-3 w-3" : "px-3 py-1 text-sm"
    };

    if (dot) {
      return (
        <span
          ref={ref}
          className={cn(
            "rounded-full",
            variants[variant],
            sizes[size],
            className
          )}
          {...props}
        />
      );
    }

    return (
      <span
        ref={ref}
        className={cn(
          baseStyles,
          variants[variant],
          sizes[size],
          className
        )}
        {...props}
      >
        {children}
      </span>
    );
  }
);

Badge.displayName = "Badge";

export { Badge };
export type { BadgeProps };
