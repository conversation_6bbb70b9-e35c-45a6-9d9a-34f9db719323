'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Card, CardHeader, CardContent, CardFooter } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';

interface StudentSignupForm {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  confirmPassword: string;
  studentId: string;
  institutionId?: string;
}

export default function StudentSignupPage() {
  const router = useRouter();
  const [form, setForm] = useState<StudentSignupForm>({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: '',
    studentId: '',
    institutionId: '',
  });
  const [errors, setErrors] = useState<Partial<StudentSignupForm>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [generalError, setGeneralError] = useState('');

  const handleInputChange = (field: keyof StudentSignupForm) => (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    setForm(prev => ({ ...prev, [field]: e.target.value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
    if (generalError) {
      setGeneralError('');
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<StudentSignupForm> = {};

    if (!form.firstName.trim()) {
      newErrors.firstName = 'First name is required';
    }

    if (!form.lastName.trim()) {
      newErrors.lastName = 'Last name is required';
    }

    if (!form.email) {
      newErrors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    if (!form.password) {
      newErrors.password = 'Password is required';
    } else if (form.password.length < 8) {
      newErrors.password = 'Password must be at least 8 characters long';
    }

    if (!form.confirmPassword) {
      newErrors.confirmPassword = 'Please confirm your password';
    } else if (form.password !== form.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
    }

    if (!form.studentId.trim()) {
      newErrors.studentId = 'Student ID is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    setGeneralError('');

    try {
      const response = await fetch('/api/auth/signup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...form,
          role: 'STUDENT',
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Registration failed');
      }

      // Store token and redirect
      localStorage.setItem('token', data.token);
      router.push('/student/dashboard');
    } catch (error) {
      setGeneralError(error instanceof Error ? error.message : 'An error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 to-white flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Header */}
        <div className="text-center mb-8">
          <Link href="/" className="inline-flex items-center space-x-2 mb-6">
            <div className="h-10 w-10 rounded-lg bg-primary-600 flex items-center justify-center">
              <span className="text-white font-bold text-xl">E</span>
            </div>
            <span className="text-2xl font-bold text-text-primary">ExamPro</span>
          </Link>
          <div className="flex items-center justify-center space-x-2 mb-4">
            <span className="text-success-600 text-2xl">👨‍🎓</span>
            <Badge variant="success" size="lg">Student Registration</Badge>
          </div>
          <h1 className="text-2xl font-bold text-text-primary mb-2">Create Student Account</h1>
          <p className="text-text-secondary">Join thousands of students taking secure online exams</p>
        </div>

        {/* Registration Form */}
        <Card variant="elevated">
          <form onSubmit={handleSubmit}>
            <CardContent className="space-y-4">
              {generalError && (
                <div className="p-3 rounded-lg bg-error-50 border border-error-200">
                  <p className="text-sm text-error-700">{generalError}</p>
                </div>
              )}

              <div className="grid grid-cols-2 gap-4">
                <Input
                  label="First Name"
                  value={form.firstName}
                  onChange={handleInputChange('firstName')}
                  error={errors.firstName}
                  placeholder="John"
                  disabled={isLoading}
                />
                <Input
                  label="Last Name"
                  value={form.lastName}
                  onChange={handleInputChange('lastName')}
                  error={errors.lastName}
                  placeholder="Doe"
                  disabled={isLoading}
                />
              </div>

              <Input
                label="Email Address"
                type="email"
                value={form.email}
                onChange={handleInputChange('email')}
                error={errors.email}
                placeholder="<EMAIL>"
                disabled={isLoading}
              />

              <Input
                label="Student ID"
                value={form.studentId}
                onChange={handleInputChange('studentId')}
                error={errors.studentId}
                placeholder="STU123456"
                helperText="Your unique student identification number"
                disabled={isLoading}
              />

              <Input
                label="Institution ID (Optional)"
                value={form.institutionId}
                onChange={handleInputChange('institutionId')}
                placeholder="UNIV001"
                helperText="Leave blank if not provided by your institution"
                disabled={isLoading}
              />

              <Input
                label="Password"
                type="password"
                value={form.password}
                onChange={handleInputChange('password')}
                error={errors.password}
                placeholder="Create a strong password"
                helperText="At least 8 characters with letters and numbers"
                disabled={isLoading}
              />

              <Input
                label="Confirm Password"
                type="password"
                value={form.confirmPassword}
                onChange={handleInputChange('confirmPassword')}
                error={errors.confirmPassword}
                placeholder="Confirm your password"
                disabled={isLoading}
              />

              <div className="flex items-start space-x-2">
                <input
                  type="checkbox"
                  id="terms"
                  required
                  className="mt-1 rounded border-border-medium text-primary-600 focus:ring-primary-500"
                />
                <label htmlFor="terms" className="text-sm text-text-secondary">
                  I agree to the{' '}
                  <Link href="/terms" className="text-primary-600 hover:text-primary-700">
                    Terms of Service
                  </Link>{' '}
                  and{' '}
                  <Link href="/privacy" className="text-primary-600 hover:text-primary-700">
                    Privacy Policy
                  </Link>
                </label>
              </div>
            </CardContent>

            <CardFooter className="flex flex-col space-y-4">
              <Button
                type="submit"
                size="lg"
                className="w-full"
                loading={isLoading}
                disabled={isLoading}
              >
                Create Student Account
              </Button>

              <div className="text-center">
                <span className="text-sm text-text-secondary">
                  Already have an account?{' '}
                  <Link
                    href="/login"
                    className="text-primary-600 hover:text-primary-700 font-medium"
                  >
                    Sign in
                  </Link>
                </span>
              </div>

              <div className="text-center">
                <Link
                  href="/signup"
                  className="text-sm text-text-tertiary hover:text-text-secondary"
                >
                  ← Back to role selection
                </Link>
              </div>
            </CardFooter>
          </form>
        </Card>
      </div>
    </div>
  );
}
